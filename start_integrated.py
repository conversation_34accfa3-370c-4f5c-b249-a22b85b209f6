#!/usr/bin/env python3
"""
FraudShield Integrated Startup Script
Starts the essential services for the integrated FraudShield system:
- Frontend Server (index.html + ap.html)
- Model Service
- Ingest Service
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
import webbrowser
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedServiceManager:
    def __init__(self):
        self.processes = {}
        self.running = True

    def start_service(self, name, script, port):
        """Start a service in a subprocess."""
        try:
            logger.info(f"Starting {name}...")

            # Special handling for frontend server
            if name == 'Frontend Server':
                # Set environment variable to force specific port
                env = os.environ.copy()
                env['FRONTEND_PORT'] = str(port)
                process = subprocess.Popen(
                    [sys.executable, script],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    env=env
                )
            else:
                process = subprocess.Popen(
                    [sys.executable, script],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )

            self.processes[name] = {
                'process': process,
                'port': port,
                'script': script
            }

            # Start output monitoring thread
            threading.Thread(
                target=self.monitor_output,
                args=(name, process.stdout),
                daemon=True
            ).start()

            logger.info(f"{name} started with PID {process.pid}")
            return True

        except Exception as e:
            logger.error(f"Failed to start {name}: {str(e)}")
            return False

    def monitor_output(self, service_name, stream):
        """Monitor service output and log it."""
        try:
            for line in iter(stream.readline, ''):
                if line.strip():
                    logger.info(f"[{service_name}] {line.strip()}")
        except Exception as e:
            logger.error(f"Error monitoring {service_name} output: {str(e)}")

    def check_service_health(self, name):
        """Check if a service is running."""
        if name not in self.processes:
            return False
        process = self.processes[name]['process']
        is_running = process.poll() is None

        # For frontend server, also check if it's actually serving
        if name == 'Frontend Server' and is_running:
            try:
                import urllib.request
                port = self.processes[name]['port']
                urllib.request.urlopen(f'http://localhost:{port}/', timeout=2)
                return True
            except:
                return False

        return is_running

    def stop_service(self, name):
        """Stop a specific service."""
        if name in self.processes:
            process = self.processes[name]['process']
            logger.info(f"Stopping {name}...")
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            del self.processes[name]
            logger.info(f"{name} stopped")

    def stop_all_services(self):
        """Stop all running services."""
        logger.info("Stopping all services...")
        for name in list(self.processes.keys()):
            self.stop_service(name)
        self.running = False

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}. Shutting down...")
        self.stop_all_services()

    def run(self):
        """Main run method."""
        logger.info("🚀 FraudShield Integrated System Starting...")

        # Check if required files exist
        required_files = [
            'simple_frontend.py', 'index.html', 'ap.html',
            'config.js', 'styles.css', 'model_service.py',
            'ingest_service.py', 'fraud_detection_model.pkl'
        ]

        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)

        if missing_files:
            logger.error(f"Missing required files: {', '.join(missing_files)}")
            return False

        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # Start core services
        services = [
            ('Frontend Server', 'simple_frontend.py', 3001),
            ('Model Service', 'model_service.py', 8001),
            ('Ingest Service', 'ingest_service.py', 9001)
        ]

        for name, script, port in services:
            if not self.start_service(name, script, port):
                logger.error(f"Failed to start {name}. Stopping all services.")
                self.stop_all_services()
                return False

        # Wait for services to start
        logger.info("Waiting for services to initialize...")
        time.sleep(5)

        # Check service health
        all_healthy = True
        for name, _, port in services:
            if self.check_service_health(name):
                logger.info(f"✅ {name} is running on port {port}")
            else:
                logger.error(f"❌ {name} failed to start")
                all_healthy = False

        if not all_healthy:
            logger.error("Some services failed to start. Stopping all services.")
            self.stop_all_services()
            return False

        # Success! Display URLs and open browser
        logger.info("\n🎉 FraudShield Integrated System is ready!")
        logger.info("📱 Access URLs:")
        logger.info("   • Home Page: http://localhost:3001/")
        logger.info("   • Fraud Detection App: http://localhost:3001/ap.html")
        logger.info("   • Model Service API: http://localhost:8001")
        logger.info("   • Ingest Service API: http://localhost:9001")
        logger.info("\n🌐 Opening web browser...")

        # Open browser to the home page
        try:
            webbrowser.open('http://localhost:3001/')
        except Exception as e:
            logger.warning(f"Could not open browser: {e}")

        logger.info("\n⚡ System is running! Press Ctrl+C to stop all services")

        # Keep running until interrupted
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

        return True

def main():
    """Main entry point."""
    manager = IntegratedServiceManager()

    try:
        success = manager.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n👋 Interrupted by user")
        manager.stop_all_services()
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        manager.stop_all_services()
        sys.exit(1)

if __name__ == '__main__':
    main()
