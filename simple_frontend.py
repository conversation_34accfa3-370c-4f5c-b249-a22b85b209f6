#!/usr/bin/env python3
"""
Simple HTTP server for FraudShield frontend
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

def start_server(port=3001):
    """Start a simple HTTP server on the specified port."""
    directory = Path(__file__).parent
    
    class CustomHandler(http.server.SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory=directory, **kwargs)
        
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def do_GET(self):
            if self.path == '/':
                self.path = '/index.html'
            return super().do_GET()
        
        def log_message(self, format, *args):
            # Suppress default logging
            pass
    
    try:
        with socketserver.TCPServer(("", port), CustomHandler) as httpd:
            print(f"Frontend server started on http://localhost:{port}")
            httpd.serve_forever()
    except OSError as e:
        print(f"Error starting server on port {port}: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("Server stopped")
        sys.exit(0)

if __name__ == "__main__":
    port = int(os.environ.get('FRONTEND_PORT', 3001))
    start_server(port)
